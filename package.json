{"name": "mamta", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.5", "@google/generative-ai": "^0.24.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@react-google-maps/api": "^2.20.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.479.0", "next": "15.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet-routing-machine": "^3.2.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-leaflet": "^2.8.3", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "typescript": "^5"}}