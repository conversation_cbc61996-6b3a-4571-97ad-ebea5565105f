import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Your App Name",
  description: "App with <PERSON> Auth",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className="light">
        <body className={`${geistSans.variable} ${geistMono.variable} pt-20`}>
          <Header />

          {/* Main page content */}
          {children}

          <Footer />
        </body>
      </html>
    </ClerkProvider>
  );
}
